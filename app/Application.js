Ext.define('MyAppName.Application', {
    extend: 'Ext.app.Application',
    
    name: 'MyAppName',
    
    quickTips: false,
    platformConfig: {
        desktop: {
            quickTips: true
        }
    },

    // Testing various AST node types
    
    // 1. ArrayLiteralExpressionTree & ArgumentListTree
    testArrays: [1, 2, 3, 'test', true, null],
    
    // 2. ObjectLiteralExpressionTree & PropertyNameAssignmentTree
    config: {
        debug: true,
        version: '1.0.0',
        features: ['grid', 'forms', 'charts']
    },
    
    // 3. FunctionDeclarationTree & FormalParameterListTree
    launch: function(profile) {
        const testObj = { one: 1, two: 2 };
        Object.entries(testObj).forEach(([key, value]) => {
            console.log(`key: ${key}, value: ${value}`);
        });
        // 4. BlockTree & VariableDeclarationTree
        var me = this,
            viewport,
            mainPanel;
            
        // 5. CallExpressionTree & MemberExpressionTree
        console.log('Application launched with profile:', profile);
        
        // 6. ConditionalExpressionTree & BinaryOperatorTree
        var isDevelopment = (profile && profile.name === 'development') ? true : false;
        
        // 7. IfStatementTree
        if (isDevelopment) {
            // 8. ExpressionStatementTree
            this.enableDebugMode();
        }
        
        // 9. TryStatementTree, CatchTree, FinallyTree
        try {
            viewport = this.createViewport();
        } catch (error) {
            console.error('Failed to create viewport:', error);
            this.handleError(error);
        } finally {
            this.initializeComplete = true;
        }
        
        // 10. ForStatementTree & UpdateExpressionTree
        for (var i = 0; i < this.testArrays.length; i++) {
            console.log('Array item:', this.testArrays[i]);
        }
        
        // 11. ForInStatementTree
        for (var key in this.config) {
            console.log('Config key:', key, 'value:', this.config[key]);
        }
        
        // 12. WhileStatementTree
        var counter = 0;
        while (counter < 5) {
            counter++;
        }
        
        // 13. DoWhileStatementTree
        do {
            console.log('Do-while iteration:', counter);
            counter--;
        } while (counter > 0);

        let object = { myProperty: "hello"};
		let property = object?.myProperty.big();

        console.log(property);
            
        // 14. SwitchStatementTree, CaseClauseTree, DefaultClauseTree
        switch (profile?.name) {
            case 'development':
                this?.setLogLevel?.('debug');
                break;
            case 'production':
                this?.setLogLevel?.('error');
                break;
            default:
                this?.setLogLevel?.('info');
        }
        
        // 15. ReturnStatementTree
        return viewport;
    },
    
    // 16. ArrowFunction with various parameter patterns
    processData: function() {
        // 17. ArrayPatternTree - Destructuring
        var [first, second, ...rest] = this.testArrays;
        
        // 18. ObjectPatternTree - Object destructuring  
        var {debug, version} = this.config;
        
        // 19. CallExpressionTree with arrow functions
        var doubled = this.testArrays.filter(item => item !== null)
                                   .map(item => item * 2)
                                   .reduce((acc, val) => acc + val, 0);
        
        // 20. TemplateLiteralExpressionTree
        var message = `Processing complete. Result: ${doubled}, Version: ${version}`;
        
        // 21. SpreadExpressionTree
        var newArray = [...this.testArrays, 4, 5, 6];
        
        return {first, second, rest, debug, version, doubled, message, newArray};
    },
    
    // 22. AsyncFunction & AwaitExpressionTree
    loadData: async function() {
        try {
            // 23. AwaitExpressionTree
            var response = await fetch('/api/data');
            var data = await response.json();
            return data;
        } catch (error) {
            throw new Error('Data loading failed: ' + error.message);
        }
    },
    
    // 24. GeneratorFunction & YieldExpressionTree
    *dataGenerator() {
        for (let item of this.testArrays) {
            yield item * 2;
        }
        yield* this.config.features; // yield delegation
    },
    
    // 25. ClassDeclarationTree (ES6 class)
    createUtilityClass: function() {
        class DataProcessor {
            constructor(data) {
                this.data = data;
            }
            
            // 26. GetAccessorTree & SetAccessorTree
            get length() {
                return this.data.length;
            }
            
            set length(value) {
                this.data.length = value;
            }
            
            // 27. ComputedPropertyMethodTree
            ['process' + 'Data']() {
                return this.data.map(item => item.toString());
            }
        }
        
        return DataProcessor;
    },
    
    // 28. NewExpressionTree
    createViewport: function() {
        // 29. MemberLookupExpressionTree
        var panelClass = Ext.panel['Panel'];
        
        return new Ext.container.Viewport({
            layout: 'border',
            items: [
                // 30. ObjectSpreadTree
                {
                    region: 'north',
                    ...this.getToolbarConfig(),
                    height: 50
                },
                {
                    region: 'center',
                    xtype: 'panel',
                    // 31. CommaExpressionTree
                    html: (this?.incrementCounter(), this.getMessage())
                }
            ]
        });
    },
    
    // 32. DefaultParameterTree
    getMessage: function(prefix = 'Default message', suffix = '!') {
        return prefix + ' from ExtJS application' + suffix;
    },
    
    // 33. RestParameterTree equivalent
    combineMessages: function() {
        const employee = { name: 'Gary', age: 28 };
        //const { name, age } = employee;  //  If run the first two lines then it is working 
        const { name, age, company = 'Eastland' } = employee; // If run the first and last line then it is throwing an error
        let object = { myProperty: "hello"};
		let property = object?.myProperty.big();
        console.log(property);
        
        console.log(name, age, company);
        // return messages.join(' ');
    },
    
    // 34. UnaryExpressionTree
    enableDebugMode: function() {
        this.debugEnabled = !this.debugEnabled;
        var isProduction = !true; // negation
        var typeCheck = typeof this.config; // typeof operator
        delete this.temporaryData; // delete operator
    },
    
    // 35. UpdateExpressionTree (pre/post increment)
    incrementCounter: function() {
        this.counter = this.counter || 0;
        return ++this.counter; // pre-increment
    },
    
    // 36. ConditionalExpressionTree (ternary)
    getLogLevel: function() {
        return this.debugEnabled ? 'debug' : 'info';
    },
    
    // 37. LabelledStatementTree
    processLoop: function() {
        outer: for (let i = 0; i < 3; i++) {
            inner: for (let j = 0; j < 3; j++) {
                if (i === 1 && j === 1) {
                    break outer; // 38. BreakStatementTree with label
                }
                if (j === 0) {
                    continue inner; // 39. ContinueStatementTree with label
                }
            }
        }
    },
    
    // 40. ThrowStatementTree
    handleError: function(error) {
        if (!error.handled) {
            throw new Error('Unhandled application error: ' + error.message);
        }
    },
    
    // 42. DebuggerStatementTree
    debugPoint: function() {
        debugger; // breakpoint for debugging
    },
    
    // 43. EmptyStatementTree
    emptyOperation: function() {
        ; // empty statement
    },
    
    // 44. ThisExpressionTree
    bindContext: function() {
        var self = this;
        return function() {
            return self.name; // 'this' context binding
        };
    },
    
    // 45. SuperExpressionTree (would be in extended class)
    callParent: function() {
        // In ExtJS context
        this.callParent(arguments);
    },
    
    // 46. NewTargetExpressionTree (ES6)
    // constructor: function() {
    //     console.log('Constructor called with new.target:', new.target);
    // },
    
    // 47. ImportDeclarationTree & ExportDeclarationTree (ES6 modules)
    // Note: These would typically be at module level
    setupModules: function() {
        // Simulated import/export for testing
        var moduleExports = {
            utils: this.getUtilities(),
            config: this.config
        };
        return moduleExports;
    },
    
    // 48. OptionalChainingExpressionTree (ES2020)
    safeAccess: function(obj) {
        return obj?.data?.items?.[0]?.name();
    },
    
    // 49. NullishCoalescingExpressionTree (ES2020)
    getValueWithDefault: function(value) {
        return value ?? 'default value';
    },
    
    // 50. Various literal types
    literals: {
        // LiteralExpressionTree
        string: 'test string',
        number: 42,
        boolean: true,
        null: null,
        undefined: undefined,
        regex: /test\d+/gi
    },
    
    // Helper methods to ensure all patterns are covered
    getToolbarConfig: function() {
        return {
            xtype: 'toolbar',
            dock: 'top',
            items: ['->',  {
                text: 'Test Button',
                handler: this.onTestClick,
                scope: this
            }]
        };
    },
    
    onTestClick: function() {
        // Comprehensive test of various expression types
        var result = this.processData();
        console.log('Test result:', result);
        
        // Test generator
        var generator = this.dataGenerator();
        console.log('Generator result:', generator.next().value);
        
        // Test async operation
        this.loadData().then(data => {
            console.log('Async data loaded:', data);
        }).catch(error => {
            console.error('Async error:', error);
        });
    },
    
    getUtilities: function() {
        return {
            format: function(template, ...args) {
                return template.replace(/{(\d+)}/g, (match, index) => {
                    return args[index] !== undefined ? args[index] : match;
                });
            }
        };
    }
});